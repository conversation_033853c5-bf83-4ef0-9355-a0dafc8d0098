import React from 'react';

interface ApiKeysTabProps {
  apiKeys: {
    openai: string;
    assemblyai: string;
  };
  errors: {
    openai?: string;
    assemblyai?: string;
  };
  testResults: {
    openai?: boolean;
    assemblyai?: boolean;
  };
  isTestingOpenAI: boolean;
  isTestingAssemblyAI: boolean;
  handleApiKeyChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  testOpenAIConnection: () => void;
  testAssemblyAIConnection: () => void;
}

const ApiKeysTab: React.FC<ApiKeysTabProps> = ({
  apiKeys,
  errors,
  testResults,
  isTestingOpenAI,
  isTestingAssemblyAI,
  handleApiKeyChange,
  testOpenAIConnection,
  testAssemblyAIConnection
}) => {
  return (
    <div className="api-keys-tab">
      <div className="api-keys-section">
        <div className="form-group">
          <div className="api-key-header">
            <label htmlFor="openai">OpenAI API Key</label>
            <a
              href="#"
              onClick={(e) => {
                e.preventDefault();
                window.open('https://platform.openai.com/api-keys', '_blank');
              }}
              className="get-key-link"
            >
              Get a key
            </a>
          </div>
          <div className="api-key-input-group">
            <input
              type="password"
              id="openai"
              name="openai"
              value={apiKeys.openai}
              onChange={handleApiKeyChange}
              placeholder="sk-..."
              disabled={isTestingOpenAI || isTestingAssemblyAI}
              className={errors.openai ? 'error' : ''}
            />
            <button
              type="button"
              onClick={testOpenAIConnection}
              disabled={isTestingOpenAI || isTestingAssemblyAI || !apiKeys.openai.trim()}
              className="test-button"
            >
              {isTestingOpenAI ? 'Testing...' : 'Test'}
            </button>
          </div>
          {errors.openai && <div className="error-message">{errors.openai}</div>}
          {testResults.openai !== undefined && (
            <div className={`test-result ${testResults.openai ? 'success' : 'failure'}`}>
              {testResults.openai
                ? 'Connection successful!'
                : 'Connection failed. Please check your API key.'}
            </div>
          )}
        </div>

        <div className="form-group">
          <div className="api-key-header">
            <label htmlFor="assemblyai">AssemblyAI API Key</label>
            <a
              href="#"
              onClick={(e) => {
                e.preventDefault();
                window.open('https://www.assemblyai.com/app/account', '_blank');
              }}
              className="get-key-link"
            >
              Get a key
            </a>
          </div>
          <div className="api-key-input-group">
            <input
              type="password"
              id="assemblyai"
              name="assemblyai"
              value={apiKeys.assemblyai}
              onChange={handleApiKeyChange}
              placeholder="Your AssemblyAI API key"
              disabled={isTestingOpenAI || isTestingAssemblyAI}
              className={errors.assemblyai ? 'error' : ''}
            />
            <button
              type="button"
              onClick={testAssemblyAIConnection}
              disabled={isTestingOpenAI || isTestingAssemblyAI || !apiKeys.assemblyai.trim()}
              className="test-button"
            >
              {isTestingAssemblyAI ? 'Testing...' : 'Test'}
            </button>
          </div>
          {errors.assemblyai && <div className="error-message">{errors.assemblyai}</div>}
          {testResults.assemblyai !== undefined && (
            <div className={`test-result ${testResults.assemblyai ? 'success' : 'failure'}`}>
              {testResults.assemblyai
                ? 'Connection successful!'
                : 'Connection failed. Please check your API key.'}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ApiKeysTab;
