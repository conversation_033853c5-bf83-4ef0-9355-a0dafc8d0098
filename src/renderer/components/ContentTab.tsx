import React from 'react';

interface ContentTabProps {
  settings: {
    resume: string;
    jobDescription: string;
    additionalContext: string;
  };
  handleChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
}

const ContentTab: React.FC<ContentTabProps> = ({ settings, handleChange }) => {
  return (
    <div className="content-tab">
      <div className="form-group">
        <label htmlFor="resume">Resume</label>
        <textarea
          id="resume"
          name="resume"
          value={settings.resume}
          onChange={handleChange}
          placeholder="Paste your resume here"
          rows={6}
        />
      </div>

      <div className="form-group">
        <label htmlFor="jobDescription">Job Description</label>
        <textarea
          id="jobDescription"
          name="jobDescription"
          value={settings.jobDescription}
          onChange={handleChange}
          placeholder="Paste the job description here"
          rows={6}
        />
      </div>

      <div className="form-group">
        <label htmlFor="additionalContext">Additional Context</label>
        <textarea
          id="additionalContext"
          name="additionalContext"
          value={settings.additionalContext}
          onChange={handleChange}
          placeholder="Add any additional context that might help generate better answers"
          rows={4}
        />
      </div>
    </div>
  );
};

export default ContentTab;
