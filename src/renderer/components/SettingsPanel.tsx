import React, { useState, useEffect } from 'react';
import './SettingsPanel.css';
import ApiKeysTab from './ApiKeysTab';
import ContentTab from './ContentTab';
import ShortcutsTab from './ShortcutsTab';

interface SettingsPanelProps {
  settings: {
    resume: string;
    jobDescription: string;
    additionalContext: string;
  };
  onSave: (settings: {
    resume: string;
    jobDescription: string;
    additionalContext: string;
  }) => void;
  onClose: () => void;
}

interface ApiKeys {
  openai: string;
  assemblyai: string;
}

const SettingsPanel: React.FC<SettingsPanelProps> = ({ settings, onSave, onClose }) => {
  const [localSettings, setLocalSettings] = useState(settings);
  const [apiKeys, setApiKeys] = useState<ApiKeys>({ openai: '', assemblyai: '' });
  const [isLoadingKeys, setIsLoadingKeys] = useState(true);
  const [isTestingOpenAI, setIsTestingOpenAI] = useState(false);
  const [isTestingAssemblyAI, setIsTestingAssemblyAI] = useState(false);
  const [testResults, setTestResults] = useState<{
    openai?: boolean;
    assemblyai?: boolean;
  }>({});
  const [errors, setErrors] = useState<{
    openai?: string;
    assemblyai?: string;
  }>({});
  const [activeTab, setActiveTab] = useState<'content' | 'api-keys' | 'shortcuts'>('content');

  // Load API keys on mount
  useEffect(() => {
    const loadApiKeys = async () => {
      try {
        const keys = await window.electronAPI.getApiKeys();
        setApiKeys(keys);
      } catch (error) {
        console.error('Error loading API keys:', error);
      } finally {
        setIsLoadingKeys(false);
      }
    };

    loadApiKeys();
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement | HTMLInputElement>) => {
    const { name, value } = e.target;
    setLocalSettings({
      ...localSettings,
      [name]: value
    });
  };

  const handleApiKeyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setApiKeys(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error and test result when user types
    if (errors[name as keyof typeof errors]) {
      setErrors(prev => ({
        ...prev,
        [name]: undefined
      }));
    }

    if (testResults[name as keyof typeof testResults] !== undefined) {
      setTestResults(prev => ({
        ...prev,
        [name]: undefined
      }));
    }
  };

  const testOpenAIConnection = async () => {
    if (!apiKeys.openai.trim()) {
      setErrors(prev => ({
        ...prev,
        openai: 'Please enter an OpenAI API key'
      }));
      return;
    }

    try {
      setIsTestingOpenAI(true);
      const result = await window.electronAPI.testOpenAIConnection(apiKeys.openai);
      setTestResults(prev => ({
        ...prev,
        openai: result.success
      }));

      if (!result.success) {
        setErrors(prev => ({
          ...prev,
          openai: 'Failed to connect to OpenAI API. Please check your key.'
        }));
      }
    } catch (error) {
      console.error('Error testing OpenAI connection:', error);
      setTestResults(prev => ({
        ...prev,
        openai: false
      }));
      setErrors(prev => ({
        ...prev,
        openai: 'Error testing connection. Please try again.'
      }));
    } finally {
      setIsTestingOpenAI(false);
    }
  };

  const testAssemblyAIConnection = async () => {
    if (!apiKeys.assemblyai.trim()) {
      setErrors(prev => ({
        ...prev,
        assemblyai: 'Please enter an AssemblyAI API key'
      }));
      return;
    }

    try {
      setIsTestingAssemblyAI(true);
      const result = await window.electronAPI.testAssemblyAIConnection(apiKeys.assemblyai);
      setTestResults(prev => ({
        ...prev,
        assemblyai: result.success
      }));

      if (!result.success) {
        setErrors(prev => ({
          ...prev,
          assemblyai: 'Failed to connect to AssemblyAI API. Please check your key.'
        }));
      }
    } catch (error) {
      console.error('Error testing AssemblyAI connection:', error);
      setTestResults(prev => ({
        ...prev,
        assemblyai: false
      }));
      setErrors(prev => ({
        ...prev,
        assemblyai: 'Error testing connection. Please try again.'
      }));
    } finally {
      setIsTestingAssemblyAI(false);
    }
  };

  const saveApiKeys = async () => {
    try {
      await window.electronAPI.saveApiKeys(apiKeys);
      return true;
    } catch (error) {
      console.error('Error saving API keys:', error);
      return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Save user settings
    onSave(localSettings);

    // Save API keys if they've changed
    await saveApiKeys();
  };

  return (
    <div className="settings-overlay">
      <div className="settings-panel">
        <div className="settings-header">
          <h2>Settings</h2>
          <button className="close-button" onClick={onClose}>×</button>
        </div>

        <form onSubmit={handleSubmit} className="settings-form">
          <div className="settings-tabs">
            <button
              type="button"
              className={`tab-button ${activeTab === 'content' ? 'active' : ''}`}
              onClick={() => setActiveTab('content')}
            >
              Content
            </button>
            <button
              type="button"
              className={`tab-button ${activeTab === 'api-keys' ? 'active' : ''}`}
              onClick={() => setActiveTab('api-keys')}
            >
              API Keys
            </button>
            <button
              type="button"
              className={`tab-button ${activeTab === 'shortcuts' ? 'active' : ''}`}
              onClick={() => setActiveTab('shortcuts')}
            >
              Keyboard Shortcuts
            </button>
          </div>

          <div className="settings-section">
            {activeTab === 'content' && (
              <ContentTab
                settings={localSettings}
                handleChange={handleChange}
              />
            )}

            {activeTab === 'api-keys' && (
              <ApiKeysTab
                apiKeys={apiKeys}
                errors={errors}
                testResults={testResults}
                isTestingOpenAI={isTestingOpenAI}
                isTestingAssemblyAI={isTestingAssemblyAI}
                handleApiKeyChange={handleApiKeyChange}
                testOpenAIConnection={testOpenAIConnection}
                testAssemblyAIConnection={testAssemblyAIConnection}
              />
            )}

            {activeTab === 'shortcuts' && (
              <ShortcutsTab />
            )}
          </div>

          <div className="settings-footer">
            <button type="button" className="cancel-button" onClick={onClose}>
              Cancel
            </button>
            <button type="submit" className="save-button">
              Save Settings
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default SettingsPanel;
