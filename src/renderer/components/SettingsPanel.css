.settings-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.settings-panel {
  background-color: var(--background-color);
  border-radius: 8px;
  width: 80%;
  max-width: 700px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  animation: slide-up 0.3s ease-out;
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid var(--border-color);
}

.settings-header h2 {
  font-size: 18px;
  font-weight: 500;
}

.close-button {
  background: none;
  border: none;
  color: var(--text-color);
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

.settings-form {
  padding: 20px;
  overflow-y: auto;
  max-height: 70vh;
}

.settings-section {
  margin-bottom: 25px;
}

.settings-section h3 {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-color);
}

/* Tabs styling */
.settings-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 20px;
}

.tab-button {
  padding: 10px 20px;
  background: none;
  border: none;
  color: var(--text-color);
  font-size: 14px;
  cursor: pointer;
  opacity: 0.7;
  transition: all 0.2s ease;
  position: relative;
}

.tab-button:hover {
  opacity: 1;
}

.tab-button.active {
  opacity: 1;
  font-weight: 500;
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--secondary-color);
}

/* Shortcuts tab styling */
.shortcuts-tab {
  padding: 10px 0;
}

.shortcuts-description {
  margin-bottom: 20px;
  font-size: 14px;
  color: var(--text-color);
  opacity: 0.8;
}

.shortcuts-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.shortcut-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.shortcut-keys {
  display: flex;
  align-items: center;
  gap: 4px;
}

.shortcut-keys kbd {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  padding: 2px 6px;
  font-size: 12px;
  font-family: monospace;
}

.shortcut-description {
  font-size: 14px;
  color: var(--text-color);
}

.api-keys-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.api-key-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.get-key-link {
  font-size: 12px;
  color: var(--secondary-color);
  text-decoration: none;
}

.get-key-link:hover {
  text-decoration: underline;
}

.api-key-input-group {
  display: flex;
  gap: 10px;
}

.api-key-input-group input {
  flex: 1;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid var(--border-color);
  background-color: rgba(255, 255, 255, 0.05);
  color: var(--text-color);
  font-size: 14px;
}

.api-key-input-group input:focus {
  outline: none;
  border-color: var(--secondary-color);
}

.api-key-input-group input.error {
  border-color: #e74c3c;
}

.test-button {
  padding: 10px 15px;
  background-color: rgba(59, 130, 246, 0.2);
  border: 1px solid rgba(59, 130, 246, 0.3);
  color: var(--text-color);
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.test-button:hover:not(:disabled) {
  background-color: rgba(59, 130, 246, 0.3);
  border-color: rgba(59, 130, 246, 0.4);
}

.test-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.error-message {
  color: #e74c3c;
  font-size: 12px;
  margin-top: 5px;
}

.test-result {
  font-size: 12px;
  padding: 5px 10px;
  border-radius: 4px;
  display: inline-block;
  margin-top: 5px;
}

.test-result.success {
  background-color: rgba(16, 185, 129, 0.2);
  color: #10b981;
}

.test-result.failure {
  background-color: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  font-size: 14px;
}

.form-group textarea {
  width: 100%;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid var(--border-color);
  background-color: rgba(255, 255, 255, 0.05);
  color: var(--text-color);
  font-size: 14px;
  resize: vertical;
}

.form-group textarea:focus {
  outline: none;
  border-color: var(--secondary-color);
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.3);
}

.settings-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding-top: 10px;
  border-top: 1px solid var(--border-color);
}

.cancel-button, .save-button {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.cancel-button {
  background-color: transparent;
  border: 1px solid var(--border-color);
  color: var(--text-color);
}

.cancel-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.save-button {
  background-color: var(--secondary-color);
  border: none;
  color: white;
}

.save-button:hover {
  background-color: #2980b9;
}
