import React, { memo, useMemo } from 'react';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';
import './AnswerPanel.css';

interface AnswerPanelProps {
  question: string;
  answer: string;
  isGenerating: boolean;
}

// Use memo to prevent unnecessary re-renders
const AnswerPanel: React.FC<AnswerPanelProps> = memo(({ question, answer, isGenerating }) => {
  return (
    <div className="answer-panel">
      <div className="panel-header">
        <h2>Suggested Answer</h2>
        {isGenerating && <div className="generating-indicator">Generating...</div>}
      </div>

      <div className="answer-content">
        {!question ? (
          <div className="no-answer">
            <p>Select a question to generate an answer.</p>
          </div>
        ) : (
          <>
            <div className="selected-question">
              <h3>Question:</h3>
              <p>{question}</p>
            </div>

            <div className="answer-text">
              <h3>Answer:</h3>
              {answer ? (
                <div className="answer-body">
                  {useMemo(() => {
                    // Memoize the markdown rendering to prevent unnecessary re-renders
                    return (
                      <ReactMarkdown
                        components={{
                          code({node, inline, className, children, ...props}) {
                            const match = /language-(\w+)/.exec(className || '');
                            return !inline && match ? (
                              <SyntaxHighlighter
                                style={vscDarkPlus}
                                language={match[1]}
                                PreTag="div"
                                {...props}
                              >
                                {String(children).replace(/\n$/, '')}
                              </SyntaxHighlighter>
                            ) : (
                              <code className={className} {...props}>
                                {children}
                              </code>
                            );
                          }
                        }}
                      >
                        {answer}
                      </ReactMarkdown>
                    );
                  }, [answer])}
                </div>
              ) : (
                <div className="no-answer">
                  <p>Press <kbd>⌘</kbd> + <kbd>Enter</kbd> to generate an answer.</p>
                </div>
              )}
            </div>
          </>
        )}
      </div>


    </div>
  );
});

export default AnswerPanel;
