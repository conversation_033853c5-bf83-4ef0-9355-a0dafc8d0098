import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';

// Define the API exposed to the renderer process
contextBridge.exposeInMainWorld('electronAPI', {
  // Transcript functions
  getTranscript: () => ipcRenderer.invoke('get-transcript'),
  clearTranscript: () => ipcRenderer.invoke('clear-transcript'),
  startTranscription: () => ipcRenderer.invoke('start-transcription'),
  stopTranscription: () => ipcRenderer.invoke('stop-transcription'),
  isRecording: () => ipcRenderer.invoke('is-recording'),
  onTranscriptUpdate: (callback: (data: { text: string, isPartial: boolean }) => void) => {
    ipcRenderer.on('transcript-update', (_, data) => callback(data));
    return () => ipcRenderer.removeAllListeners('transcript-update');
  },
  onTranscriptCleared: (callback: () => void) => {
    ipcRenderer.on('transcript-cleared', () => callback());
    return () => ipcRenderer.removeAllListeners('transcript-cleared');
  },

  // Question extraction and answer generation
  extractQuestions: () => ipcRenderer.invoke('extract-questions'),
  onQuestionsExtracted: (callback: (questions: string[]) => void) => {
    ipcRenderer.on('questions-extracted', (_, questions) => callback(questions));
    return () => ipcRenderer.removeAllListeners('questions-extracted');
  },
  generateAnswer: (question: string) => ipcRenderer.invoke('generate-answer', question),
  onGenerateAnswerRequest: (callback: () => void) => {
    ipcRenderer.on('generate-answer-request', () => callback());
    return () => ipcRenderer.removeAllListeners('generate-answer-request');
  },

  // User settings
  getUserSettings: () => ipcRenderer.invoke('get-user-settings'),
  saveUserSettings: (settings: {
    resume: string;
    jobDescription: string;
    additionalContext: string;
  }) => ipcRenderer.invoke('save-user-settings', settings),

  // API key management
  getApiKeys: () => ipcRenderer.invoke('get-api-keys'),
  saveApiKeys: (keys: { openai: string, assemblyai: string }) => ipcRenderer.invoke('save-api-keys', keys),
  testOpenAIConnection: (apiKey?: string) => ipcRenderer.invoke('test-openai-connection', apiKey),
  testAssemblyAIConnection: (apiKey?: string) => ipcRenderer.invoke('test-assemblyai-connection', apiKey),
  clearApiKeys: () => ipcRenderer.invoke('clear-api-keys'),

  // Window management
  toggleWindow: () => ipcRenderer.invoke('toggle-window'),
  onToggleWindow: (callback: () => void) => {
    ipcRenderer.on('toggle-window', () => callback());
    return () => ipcRenderer.removeAllListeners('toggle-window');
  },
  onSetView: (callback: (view: string) => void) => {
    ipcRenderer.on('set-view', (_, view) => callback(view));
    return () => ipcRenderer.removeAllListeners('set-view');
  },

  // Recording status
  onRecordingStatusChanged: (callback: (data: { isRecording: boolean }) => void) => {
    ipcRenderer.on('recording-status-changed', (_, data) => callback(data));
    return () => ipcRenderer.removeAllListeners('recording-status-changed');
  },

  // App restart functionality
  restartApp: () => ipcRenderer.invoke('restart-app')
});
