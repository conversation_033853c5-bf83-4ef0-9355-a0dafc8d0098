const { execSync } = require('child_process');
const { existsSync } = require('fs');
const path = require('path');
const os = require('os');
const { app } = require('electron');

/**
 * Check if Sox is installed and accessible
 */
function checkSoxInstalled() {
  try {
    // Try to execute sox --version
    execSync('sox --version', { stdio: 'ignore' });
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * Show a dialog to inform the user about Sox installation
 */
function showSoxInstallationDialog() {
  const { dialog } = require('electron');
  
  dialog.showMessageBox({
    type: 'warning',
    title: 'Sox Required',
    message: 'The Sox audio tool is required for recording functionality.',
    detail: 'Please install <PERSON> to use the recording features:\n\n' +
            'macOS: brew install sox\n' +
            'Windows: Download from sourceforge.net/projects/sox/\n' +
            'Linux: apt-get install sox or equivalent',
    buttons: ['OK'],
    defaultId: 0
  });
}

/**
 * Check for Sox and show installation instructions if needed
 */
function ensureSoxAvailable() {
  const isSoxInstalled = checkSoxInstalled();
  
  if (!isSoxInstalled) {
    showSoxInstallationDialog();
    return false;
  }
  
  return true;
}

module.exports = {
  ensureSoxAvailable
};
