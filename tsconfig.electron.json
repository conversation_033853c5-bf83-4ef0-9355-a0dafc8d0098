{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "moduleResolution": "node", "esModuleInterop": true, "sourceMap": true, "outDir": "dist/electron", "baseUrl": ".", "paths": {"*": ["node_modules/*"]}, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictBindCallApply": true, "strictPropertyInitialization": true, "noImplicitThis": true, "alwaysStrict": true}, "include": ["src/electron/**/*"]}