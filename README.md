# Interview Assistant

An Electron.js application that helps with interview preparation by providing real-time transcription and AI-powered answers to interview questions.

## Features

- **Real-time Transcription**: Captures and displays your speech in real-time
- **Question Extraction**: Automatically extracts interview questions from the transcript
- **AI-Powered Answers**: Generates tailored answers to interview questions using OpenAI's GPT-4o-mini model
- **Customizable Context**: Add your resume, job description, and additional context to tailor answers
- **Global Shortcuts**:
  - `Cmd+B` (or `Ctrl+B` on Windows/Linux): Toggle window visibility
  - `Cmd+H` (or `Ctrl+H`): Extract questions from transcript
  - `Cmd+R` (or `Ctrl+R`): Clear transcript
  - `Cmd+Enter` (or `Ctrl+Enter`): Generate answer for selected question

## Prerequisites

- Node.js (v16 or higher)
- npm (v8 or higher)
- Sox (for audio recording)
  - macOS: `brew install sox`
  - Linux: `apt-get install sox`
  - Windows: Download from [Sox website](https://sourceforge.net/projects/sox/)

## Installation

1. Clone the repository:
   ```
   git clone <repository-url>
   cd InterviewAssistant
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Create a `.env` file in the root directory with your API keys:
   ```
   OPENAI_API_KEY=your_openai_api_key
   ASSEMBLY_API_KEY=your_assemblyai_api_key
   ```

## Development

To run the application in development mode:

```
npm run dev
```

This will start both the Electron process and the Vite development server.

## Building

To build the application:

```
npm run build
```

To package the application for distribution:

```
npm run package
```

## Usage

1. Start the application using the provided start script:
   ```
   bash start.sh
   ```

2. The application will appear as a floating window on your screen.

3. As you speak, the application will transcribe your speech in real-time.

4. Use the global shortcuts to interact with the application:
   - `Cmd+H` to extract questions from the transcript
   - `Cmd+Enter` to generate an answer for the selected question
   - `Cmd+R` to clear the transcript
   - `Cmd+B` to toggle the window visibility

5. Click the Settings button (⚙️) to add your resume, job description, and additional context to tailor the answers to your specific situation.

## License

MIT
