#!/bin/bash

# Create temporary iconset directory
mkdir -p assets/icon.iconset

# Generate the various icon sizes
sips -z 16 16     assets/icon.png --out assets/icon.iconset/icon_16x16.png
sips -z 32 32     assets/icon.png --out assets/icon.iconset/<EMAIL>
sips -z 32 32     assets/icon.png --out assets/icon.iconset/icon_32x32.png
sips -z 64 64     assets/icon.png --out assets/icon.iconset/<EMAIL>
sips -z 128 128   assets/icon.png --out assets/icon.iconset/icon_128x128.png
sips -z 256 256   assets/icon.png --out assets/icon.iconset/<EMAIL>
sips -z 256 256   assets/icon.png --out assets/icon.iconset/icon_256x256.png
sips -z 512 512   assets/icon.png --out assets/icon.iconset/<EMAIL>
sips -z 512 512   assets/icon.png --out assets/icon.iconset/icon_512x512.png
sips -z 1024 1024 assets/icon.png --out assets/icon.iconset/<EMAIL>

# Convert the iconset to icns
iconutil -c icns assets/icon.iconset -o assets/icon.icns

# Clean up the temporary iconset directory
rm -rf assets/icon.iconset

echo "Icon conversion complete. Icon saved at assets/icon.icns"
