"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WindowHelper = void 0;
const electron_1 = require("electron");
const path_1 = __importDefault(require("path"));
const isDev = process.env.NODE_ENV === 'development';
const isMac = process.platform === 'darwin';
const startUrl = isDev
    ? 'http://localhost:5173'
    : `file://${path_1.default.join(__dirname, '../../dist/index.html')}`;
class WindowHelper {
    constructor(appState) {
        this.mainWindow = null;
        this.isWindowVisible = false;
        this.windowPosition = null;
        this.windowSize = null;
        // Initialize with explicit number type and 0 value
        this.screenWidth = 0;
        this.screenHeight = 0;
        this.step = 0;
        this.currentX = 0;
        this.currentY = 0;
        // Add this property to track focus
        this.wasFocused = false;
        this.appState = appState;
    }
    setWindowDimensions(width, height) {
        if (!this.mainWindow || this.mainWindow.isDestroyed())
            return;
        // Get current window position
        const [currentX, currentY] = this.mainWindow.getPosition();
        // Get screen dimensions
        const primaryDisplay = electron_1.screen.getPrimaryDisplay();
        const workArea = primaryDisplay.workAreaSize;
        // Use 75% width if debugging has occurred, otherwise use 60%
        const maxAllowedWidth = Math.floor(workArea.width * (this.appState.getHasDebugged() ? 0.75 : 0.4));
        // Ensure width doesn't exceed max allowed width and height is reasonable
        const newWidth = Math.min(width + 32, maxAllowedWidth);
        const newHeight = Math.ceil(height);
        // Center the window horizontally if it would go off screen
        const maxX = workArea.width - newWidth;
        const newX = Math.min(Math.max(currentX, 0), maxX);
        // Update window bounds
        this.mainWindow.setBounds({
            x: newX,
            y: currentY,
            width: newWidth,
            height: newHeight
        });
        // Update internal state
        this.windowPosition = { x: newX, y: currentY };
        this.windowSize = { width: newWidth, height: newHeight };
        this.currentX = newX;
    }
    createWindow() {
        if (this.mainWindow !== null)
            return;
        const primaryDisplay = electron_1.screen.getPrimaryDisplay();
        const workArea = primaryDisplay.workAreaSize;
        this.screenWidth = workArea.width;
        this.screenHeight = workArea.height;
        this.step = Math.floor(this.screenWidth / 10); // 10 steps
        this.currentX = 0; // Start at the left
        const windowSettings = {
            width: 800,
            height: 600,
            minWidth: 400,
            minHeight: 300,
            x: this.currentX,
            y: 0,
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                preload: path_1.default.join(__dirname, 'preload.js')
            },
            show: true,
            frame: false,
            transparent: true,
            // Allow fullscreen capability for better compatibility
            fullscreenable: true,
            hasShadow: false,
            backgroundColor: '#00000000',
            focusable: true,
            alwaysOnTop: true,
            // Improved settings for macOS full screen compatibility
            skipTaskbar: true,
            // Ensure window level is appropriate for overlays
            type: 'panel'
        };
        this.mainWindow = new electron_1.BrowserWindow(windowSettings);
        this.mainWindow.setContentProtection(true);
        // Only call macOS specific methods if running on macOS
        if (isMac) {
            this.mainWindow.setHiddenInMissionControl(true);
            // Enhanced macOS full screen compatibility
            this.mainWindow.setVisibleOnAllWorkspaces(true, {
                visibleOnFullScreen: true
            });
            // For macOS 12 (Monterey) compatibility
            const osVersion = process.getSystemVersion();
            if (osVersion.startsWith('12.')) {
                // Use screen-saver level for macOS 12 to improve overlay in full screen
                this.mainWindow.setAlwaysOnTop(true, 'screen-saver');
            }
            else {
                // Use floating level for other macOS versions
                this.mainWindow.setAlwaysOnTop(true, 'floating');
            }
        }
        else {
            this.mainWindow.setAlwaysOnTop(true, 'floating');
        }
        this.mainWindow.loadURL(startUrl).catch((err) => {
            console.error('Failed to load URL:', err);
        });
        const bounds = this.mainWindow.getBounds();
        this.windowPosition = { x: bounds.x, y: bounds.y };
        this.windowSize = { width: bounds.width, height: bounds.height };
        this.currentX = bounds.x;
        this.currentY = bounds.y;
        this.setupWindowListeners();
        this.isWindowVisible = true;
    }
    setupWindowListeners() {
        if (!this.mainWindow)
            return;
        this.mainWindow.on('move', () => {
            if (this.mainWindow) {
                const bounds = this.mainWindow.getBounds();
                this.windowPosition = { x: bounds.x, y: bounds.y };
                this.currentX = bounds.x;
                this.currentY = bounds.y;
            }
        });
        this.mainWindow.on('resize', () => {
            if (this.mainWindow) {
                const bounds = this.mainWindow.getBounds();
                this.windowSize = { width: bounds.width, height: bounds.height };
            }
        });
        this.mainWindow.on('closed', () => {
            this.mainWindow = null;
            this.isWindowVisible = false;
            this.windowPosition = null;
            this.windowSize = null;
        });
    }
    getMainWindow() {
        return this.mainWindow;
    }
    isVisible() {
        return this.isWindowVisible;
    }
    hideMainWindow() {
        if (!this.mainWindow || this.mainWindow.isDestroyed()) {
            console.warn('Main window does not exist or is destroyed.');
            return;
        }
        // Store focus state before hiding
        this.wasFocused = this.mainWindow.isFocused();
        const bounds = this.mainWindow.getBounds();
        this.windowPosition = { x: bounds.x, y: bounds.y };
        this.windowSize = { width: bounds.width, height: bounds.height };
        this.mainWindow.hide();
        this.isWindowVisible = false;
    }
    showMainWindow() {
        if (!this.mainWindow || this.mainWindow.isDestroyed()) {
            console.warn('Main window does not exist or is destroyed.');
            return;
        }
        const focusedWindow = electron_1.BrowserWindow.getFocusedWindow();
        if (this.windowPosition && this.windowSize) {
            this.mainWindow.setBounds({
                x: this.windowPosition.x,
                y: this.windowPosition.y,
                width: this.windowSize.width,
                height: this.windowSize.height
            });
        }
        // For macOS 12 (Monterey) compatibility in full screen mode
        if (isMac) {
            const osVersion = process.getSystemVersion();
            if (osVersion.startsWith('12.')) {
                // Ensure window is visible on all workspaces including full screen
                this.mainWindow.setVisibleOnAllWorkspaces(true, {
                    visibleOnFullScreen: true
                });
                // Reset the always-on-top property to ensure it takes effect
                this.mainWindow.setAlwaysOnTop(false);
                this.mainWindow.setAlwaysOnTop(true, 'screen-saver');
            }
        }
        this.mainWindow.showInactive();
        if (focusedWindow && !focusedWindow.isDestroyed()) {
            focusedWindow.focus();
        }
        this.isWindowVisible = true;
    }
    toggleMainWindow() {
        if (this.isWindowVisible) {
            this.hideMainWindow();
        }
        else {
            this.showMainWindow();
        }
    }
    // Move window in specified direction
    moveWindow(direction) {
        if (!this.mainWindow || this.mainWindow.isDestroyed())
            return;
        const [currentX, currentY] = this.mainWindow.getPosition();
        const moveStep = 20; // pixels to move
        let newX = currentX;
        let newY = currentY;
        switch (direction) {
            case 'left':
                newX = Math.max(0, currentX - moveStep);
                break;
            case 'right':
                newX = currentX + moveStep;
                break;
            case 'up':
                newY = Math.max(0, currentY - moveStep);
                break;
            case 'down':
                newY = currentY + moveStep;
                break;
        }
        // Get screen dimensions to prevent moving off-screen
        const primaryDisplay = electron_1.screen.getPrimaryDisplay();
        const workArea = primaryDisplay.workAreaSize;
        const bounds = this.mainWindow.getBounds();
        // Ensure window stays within screen bounds
        newX = Math.min(newX, workArea.width - bounds.width);
        newY = Math.min(newY, workArea.height - bounds.height);
        this.mainWindow.setPosition(newX, newY);
        this.windowPosition = { x: newX, y: newY };
        this.currentX = newX;
        this.currentY = newY;
    }
}
exports.WindowHelper = WindowHelper;
//# sourceMappingURL=WindowHelper.js.map