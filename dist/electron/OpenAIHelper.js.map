{"version": 3, "file": "OpenAIHelper.js", "sourceRoot": "", "sources": ["../../src/electron/OpenAIHelper.ts"], "names": [], "mappings": ";;;;;;AAAA,oDAA4B;AAE5B,oEAAmC;AAEnC,MAAa,YAAY;IAIvB,YAAY,QAAkB;QA4C9B,+DAA+D;QACvD,oBAAe,GAKnB,EAAE,CAAC;QAEP,2DAA2D;QACnD,gBAAW,GAKf,EAAE,CAAC;QAEP,wEAAwE;QAChE,wBAAmB,GAAW,CAAC,CAAC;QAChC,kBAAa,GAAa,EAAE,CAAC;QAErC,iCAAiC;QACzB,mBAAc,GAAW,EAAE,CAAC;QAC5B,cAAS,GAAW,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,aAAa;QAjEvD,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAEzB,kDAAkD;QAClD,MAAM,KAAK,GAAG,IAAI,wBAAK,EAAE,CAAC;QAC1B,MAAM,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC,SAAS,CAAuB,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;QAC7E,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,EAAE,CAAC;QAElE,IAAI,CAAC,MAAM,GAAG,IAAI,gBAAM,CAAC;YACvB,MAAM,EAAE,MAAM;SACf,CAAC,CAAC;IACL,CAAC;IAED;;;OAGG;IACI,YAAY,CAAC,MAAc;QAChC,IAAI,CAAC,MAAM,GAAG,IAAI,gBAAM,CAAC;YACvB,MAAM,EAAE,MAAM;SACf,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,cAAc,CAAC,MAAe;QACzC,IAAI,CAAC;YACH,8EAA8E;YAC9E,MAAM,UAAU,GAAG,MAAM;gBACvB,CAAC,CAAC,IAAI,gBAAM,CAAC,EAAE,MAAM,EAAE,CAAC;gBACxB,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;YAEhB,wDAAwD;YACxD,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YAC9C,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAChC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACvD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IA0BD;;;;OAIG;IACI,KAAK,CAAC,gBAAgB,CAAC,UAAkB;QAC9C,IAAI,CAAC,UAAU,IAAI,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;YAC5C,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,wFAAwF;QACxF,mEAAmE;QACnE,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClC,MAAM,gBAAgB,GAAG,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,mBAAmB,CAAC;YACtE,8DAA8D;YAC9D,IAAI,gBAAgB,GAAG,GAAG,EAAE,CAAC;gBAC3B,OAAO,CAAC,GAAG,CAAC,mDAAmD,CAAC,CAAC;gBACjE,OAAO,IAAI,CAAC,aAAa,CAAC;YAC5B,CAAC;QACH,CAAC;QAED,mCAAmC;QACnC,IAAI,CAAC,mBAAmB,GAAG,UAAU,CAAC,MAAM,CAAC;QAE7C,uDAAuD;QACvD,MAAM,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;QAEnD,wCAAwC;QACxC,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;QACxD,IAAI,UAAU,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,UAAU,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YACvE,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;YACtC,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,SAAS,CAAC;YAC1C,OAAO,UAAU,CAAC,SAAS,CAAC;QAC9B,CAAC;QAED,6CAA6C;QAC7C,IAAI,CAAC,YAAY,EAAE,CAAC;QAEpB,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;QACjD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACzD,KAAK,EAAE,aAAa;gBACpB,QAAQ,EAAE;oBACR;wBACE,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,4EAA4E;4BAC5E,sCAAsC;4BACtC,uEAAuE;4BACvE,8FAA8F;4BAC9F,wHAAwH;4BACxH,mFAAmF;4BACnF,6EAA6E;qBACvF;oBACD;wBACE,IAAI,EAAE,MAAM;wBACZ,OAAO,EAAE,0DAA0D,UAAU,GAAG;qBACjF;iBACF;gBACD,eAAe,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE;gBACxC,WAAW,EAAE,GAAG,EAAE,gDAAgD;gBAClE,UAAU,EAAE,IAAI,CAAE,wCAAwC;aAC3D,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC;YACrD,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,OAAO,EAAE,CAAC;YACZ,CAAC;YAED,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC3B,OAAO,CAAC,GAAG,CAAC,oCAAoC,OAAO,GAAG,SAAS,IAAI,CAAC,CAAC;YAEzE,IAAI,CAAC;gBACH,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;gBAC1C,IAAI,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC;gBAEtF,2CAA2C;gBAC3C,SAAS,GAAG,SAAS;qBAClB,MAAM,CAAC,CAAC,CAAS,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,yBAAyB;qBACrE,GAAG,CAAC,CAAC,CAAS,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,kBAAkB;gBAEnD,8CAA8C;gBAC9C,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,GAAG;oBACrC,SAAS,EAAE,SAAS;oBACpB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;iBACtB,CAAC;gBAEF,4BAA4B;gBAC5B,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;gBAE/B,OAAO,CAAC,GAAG,CAAC,aAAa,SAAS,CAAC,MAAM,4BAA4B,CAAC,CAAC;gBACvE,OAAO,SAAS,CAAC;YACnB,CAAC;YAAC,OAAO,UAAU,EAAE,CAAC;gBACpB,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,UAAU,CAAC,CAAC;gBAC5D,OAAO,EAAE,CAAC;YACZ,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACpD,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;;;OAIG;IACK,UAAU,CAAC,GAAW;QAC5B,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACpC,MAAM,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAC/B,IAAI,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;YACnC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,2BAA2B;QACjD,CAAC;QACD,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;IACzB,CAAC;IAED;;OAEG;IACK,YAAY;QAClB,4BAA4B;QAC5B,MAAM,cAAc,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACzD,IAAI,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YAChD,mCAAmC;YACnC,MAAM,UAAU,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAC9C,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,SAAS,CACtE,CAAC;YAEF,wBAAwB;YACxB,MAAM,YAAY,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC;YACtF,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBACzB,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,cAAc,YAAY,CAAC,MAAM,+BAA+B,CAAC,CAAC;QAChF,CAAC;QAED,wBAAwB;QACxB,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACjD,IAAI,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YAC5C,mCAAmC;YACnC,MAAM,UAAU,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAC1C,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,SAAS,CAC9D,CAAC;YAEF,wBAAwB;YACxB,MAAM,YAAY,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC;YAClF,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBACzB,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;YAC/B,CAAC,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,cAAc,YAAY,CAAC,MAAM,2BAA2B,CAAC,CAAC;QAC5E,CAAC;IACH,CAAC;IAED;;OAEG;IACI,UAAU;QACf,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QAC1B,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACtB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;QAC7B,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;IACpC,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,cAAc,CAAC,QAAgB;QAC1C,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;YACxC,OAAO,uBAAuB,CAAC;QACjC,CAAC;QAED,oBAAoB;QACpB,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAC/C,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;QAClD,IAAI,UAAU,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,UAAU,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YACvE,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;YACnC,OAAO,UAAU,CAAC,MAAM,CAAC;QAC3B,CAAC;QAED,oBAAoB;QACpB,IAAI,CAAC,YAAY,EAAE,CAAC;QAEpB,gCAAgC;QAChC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC;QAEjD,4CAA4C;QAC5C,IAAI,OAAO,GAAG,EAAE,CAAC;QACjB,IAAI,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;YACrD,OAAO,IAAI,gBAAgB,QAAQ,CAAC,MAAM,EAAE,CAAC;QAC/C,CAAC;QAED,IAAI,QAAQ,CAAC,cAAc,IAAI,QAAQ,CAAC,cAAc,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;YACrE,OAAO,IAAI,sBAAsB,QAAQ,CAAC,cAAc,EAAE,CAAC;QAC7D,CAAC;QAED,IAAI,QAAQ,CAAC,iBAAiB,IAAI,QAAQ,CAAC,iBAAiB,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;YAC3E,OAAO,IAAI,yBAAyB,QAAQ,CAAC,iBAAiB,EAAE,CAAC;QACnE,CAAC;QAED,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;YAC9C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACzD,KAAK,EAAE,aAAa;gBACpB,WAAW,EAAE,GAAG,EAAE,+CAA+C;gBACjE,UAAU,EAAE,IAAI,EAAE,oBAAoB;gBACtC,QAAQ,EAAE;oBACR;wBACE,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,6CAA6C;4BAC7C,0GAA0G;4BAC1G,4FAA4F;4BAC5F,qDAAqD;4BACrD,yFAAyF;4BACzF,qDAAqD;4BACrD,uCAAuC;4BACvC,6DAA6D;4BAC7D,8EAA8E;4BAC9E,0GAA0G;4BAC1G,0CAA0C;4BAC1C,CAAC,OAAO,CAAC,CAAC,CAAC,yEAAyE,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;qBAC7G;oBACD;wBACE,IAAI,EAAE,MAAM;wBACZ,OAAO,EAAE,0BAA0B,QAAQ,yCAAyC;qBACrF;iBACF;aACF,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC3B,OAAO,CAAC,GAAG,CAAC,kCAAkC,OAAO,GAAG,SAAS,IAAI,CAAC,CAAC;YAEvE,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,OAAO,IAAI,+BAA+B,CAAC;YAEvF,mBAAmB;YACnB,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,GAAG;gBAC/B,MAAM,EAAE,MAAM;gBACd,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC;YAEF,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YACjD,OAAO,4CAA4C,CAAC;QACtD,CAAC;IACH,CAAC;CACF;AAtUD,oCAsUC"}