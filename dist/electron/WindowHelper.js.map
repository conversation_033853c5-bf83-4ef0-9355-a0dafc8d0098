{"version": 3, "file": "WindowHelper.js", "sourceRoot": "", "sources": ["../../src/electron/WindowHelper.ts"], "names": [], "mappings": ";;;;;;AAAA,uCAAiD;AACjD,gDAAwB;AAGxB,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAC;AACrD,MAAM,KAAK,GAAG,OAAO,CAAC,QAAQ,KAAK,QAAQ,CAAC;AAE5C,MAAM,QAAQ,GAAG,KAAK;IACpB,CAAC,CAAC,uBAAuB;IACzB,CAAC,CAAC,UAAU,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,uBAAuB,CAAC,EAAE,CAAC;AAE9D,MAAa,YAAY;IAiBvB,YAAY,QAAkB;QAhBtB,eAAU,GAAyB,IAAI,CAAC;QACxC,oBAAe,GAAY,KAAK,CAAC;QACjC,mBAAc,GAAoC,IAAI,CAAC;QACvD,eAAU,GAA6C,IAAI,CAAC;QAGpE,mDAAmD;QAC3C,gBAAW,GAAW,CAAC,CAAC;QACxB,iBAAY,GAAW,CAAC,CAAC;QACzB,SAAI,GAAW,CAAC,CAAC;QACjB,aAAQ,GAAW,CAAC,CAAC;QACrB,aAAQ,GAAW,CAAC,CAAC;QAE7B,mCAAmC;QAC3B,eAAU,GAAY,KAAK,CAAC;QAGlC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC3B,CAAC;IAEM,mBAAmB,CAAC,KAAa,EAAE,MAAc;QACtD,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE;YAAE,OAAO;QAE9D,8BAA8B;QAC9B,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC;QAE3D,wBAAwB;QACxB,MAAM,cAAc,GAAG,iBAAM,CAAC,iBAAiB,EAAE,CAAC;QAClD,MAAM,QAAQ,GAAG,cAAc,CAAC,YAAY,CAAC;QAE7C,6DAA6D;QAC7D,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAChC,QAAQ,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAC/D,CAAC;QAEF,yEAAyE;QACzE,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,EAAE,EAAE,eAAe,CAAC,CAAC;QACvD,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAEpC,2DAA2D;QAC3D,MAAM,IAAI,GAAG,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC;QACvC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;QAEnD,uBAAuB;QACvB,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC;YACxB,CAAC,EAAE,IAAI;YACP,CAAC,EAAE,QAAQ;YACX,KAAK,EAAE,QAAQ;YACf,MAAM,EAAE,SAAS;SAClB,CAAC,CAAC;QAEH,wBAAwB;QACxB,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC;QAC/C,IAAI,CAAC,UAAU,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;QACzD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACvB,CAAC;IAEM,YAAY;QACjB,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI;YAAE,OAAO;QAErC,MAAM,cAAc,GAAG,iBAAM,CAAC,iBAAiB,EAAE,CAAC;QAClD,MAAM,QAAQ,GAAG,cAAc,CAAC,YAAY,CAAC;QAC7C,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,KAAK,CAAC;QAClC,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,MAAM,CAAC;QAEpC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC,CAAC,CAAC,WAAW;QAC1D,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,oBAAoB;QAEvC,MAAM,cAAc,GAA6C;YAC/D,KAAK,EAAE,GAAG;YACV,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE,GAAG;YACb,SAAS,EAAE,GAAG;YACd,CAAC,EAAE,IAAI,CAAC,QAAQ;YAChB,CAAC,EAAE,CAAC;YACJ,cAAc,EAAE;gBACd,eAAe,EAAE,KAAK;gBACtB,gBAAgB,EAAE,IAAI;gBACtB,OAAO,EAAE,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC;aAC5C;YACD,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,KAAK;YACZ,WAAW,EAAE,IAAI;YACjB,uDAAuD;YACvD,cAAc,EAAE,IAAI;YACpB,SAAS,EAAE,KAAK;YAChB,eAAe,EAAE,WAAW;YAC5B,SAAS,EAAE,IAAI;YACf,WAAW,EAAE,IAAI;YACjB,wDAAwD;YACxD,WAAW,EAAE,IAAI;YACjB,kDAAkD;YAClD,IAAI,EAAE,OAAO;SACd,CAAC;QAEF,IAAI,CAAC,UAAU,GAAG,IAAI,wBAAa,CAAC,cAAc,CAAC,CAAC;QAEpD,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAE3C,uDAAuD;QACvD,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,UAAU,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC;YAEhD,2CAA2C;YAC3C,IAAI,CAAC,UAAU,CAAC,yBAAyB,CAAC,IAAI,EAAE;gBAC9C,mBAAmB,EAAE,IAAI;aAC1B,CAAC,CAAC;YAEH,wCAAwC;YACxC,MAAM,SAAS,GAAG,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAC7C,IAAI,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;gBAChC,wEAAwE;gBACxE,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;YACvD,CAAC;iBAAM,CAAC;gBACN,8CAA8C;gBAC9C,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;YACnD,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;QACnD,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;YAC9C,OAAO,CAAC,KAAK,CAAC,qBAAqB,EAAE,GAAG,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;QAC3C,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC;QACnD,IAAI,CAAC,UAAU,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC;QACjE,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC;QACzB,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC;QAEzB,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;IAC9B,CAAC;IAEO,oBAAoB;QAC1B,IAAI,CAAC,IAAI,CAAC,UAAU;YAAE,OAAO;QAE7B,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE;YAC9B,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBACpB,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;gBAC3C,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC;gBACnD,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC;gBACzB,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC;YAC3B,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;YAChC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBACpB,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;gBAC3C,IAAI,CAAC,UAAU,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC;YACnE,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;YAChC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;YAC7B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;YAC3B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACzB,CAAC,CAAC,CAAC;IACL,CAAC;IAEM,aAAa;QAClB,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAEM,SAAS;QACd,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;IAEM,cAAc;QACnB,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,EAAE,CAAC;YACtD,OAAO,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;YAC5D,OAAO;QACT,CAAC;QAED,kCAAkC;QAClC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;QAE9C,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;QAC3C,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC;QACnD,IAAI,CAAC,UAAU,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC;QACjE,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;QACvB,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;IAC/B,CAAC;IAEM,cAAc;QACnB,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,EAAE,CAAC;YACtD,OAAO,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;YAC5D,OAAO;QACT,CAAC;QAED,MAAM,aAAa,GAAG,wBAAa,CAAC,gBAAgB,EAAE,CAAC;QAEvD,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAC3C,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC;gBACxB,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;gBACxB,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;gBACxB,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,KAAK;gBAC5B,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM;aAC/B,CAAC,CAAC;QACL,CAAC;QAED,4DAA4D;QAC5D,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,SAAS,GAAG,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAC7C,IAAI,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;gBAChC,mEAAmE;gBACnE,IAAI,CAAC,UAAU,CAAC,yBAAyB,CAAC,IAAI,EAAE;oBAC9C,mBAAmB,EAAE,IAAI;iBAC1B,CAAC,CAAC;gBAEH,6DAA6D;gBAC7D,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBACtC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;YACvD,CAAC;QACH,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC;QAE/B,IAAI,aAAa,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,EAAE,CAAC;YAClD,aAAa,CAAC,KAAK,EAAE,CAAC;QACxB,CAAC;QAED,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;IAC9B,CAAC;IAEM,gBAAgB;QACrB,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,IAAI,CAAC,cAAc,EAAE,CAAC;QACxB,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,cAAc,EAAE,CAAC;QACxB,CAAC;IACH,CAAC;IAED,qCAAqC;IAC9B,UAAU,CAAC,SAA2C;QAC3D,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE;YAAE,OAAO;QAE9D,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC;QAC3D,MAAM,QAAQ,GAAG,EAAE,CAAC,CAAC,iBAAiB;QAEtC,IAAI,IAAI,GAAG,QAAQ,CAAC;QACpB,IAAI,IAAI,GAAG,QAAQ,CAAC;QAEpB,QAAQ,SAAS,EAAE,CAAC;YAClB,KAAK,MAAM;gBACT,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,CAAC;gBACxC,MAAM;YACR,KAAK,OAAO;gBACV,IAAI,GAAG,QAAQ,GAAG,QAAQ,CAAC;gBAC3B,MAAM;YACR,KAAK,IAAI;gBACP,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,CAAC;gBACxC,MAAM;YACR,KAAK,MAAM;gBACT,IAAI,GAAG,QAAQ,GAAG,QAAQ,CAAC;gBAC3B,MAAM;QACV,CAAC;QAED,qDAAqD;QACrD,MAAM,cAAc,GAAG,iBAAM,CAAC,iBAAiB,EAAE,CAAC;QAClD,MAAM,QAAQ,GAAG,cAAc,CAAC,YAAY,CAAC;QAC7C,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;QAE3C,2CAA2C;QAC3C,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;QACrD,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;QAEvD,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACxC,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC;QAC3C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACvB,CAAC;CACF;AAnRD,oCAmRC"}