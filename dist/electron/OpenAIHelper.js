"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OpenAIHelper = void 0;
const openai_1 = __importDefault(require("openai"));
const electron_store_1 = __importDefault(require("electron-store"));
class OpenAIHelper {
    constructor(appState) {
        // Cache for transcript processing to avoid redundant API calls
        this.transcriptCache = {};
        // Cache for answer generation to avoid redundant API calls
        this.answerCache = {};
        // Track the last processed transcript length for incremental processing
        this.lastProcessedLength = 0;
        this.lastQuestions = [];
        // Constants for cache management
        this.MAX_CACHE_SIZE = 10;
        this.CACHE_TTL = 30 * 60 * 1000; // 30 minutes
        this.appState = appState;
        // Get API key from store or environment variables
        const store = new electron_store_1.default();
        const apiKeys = store.get('apiKeys') || { openai: '' };
        const apiKey = apiKeys.openai || process.env.OPENAI_API_KEY || '';
        this.openai = new openai_1.default({
            apiKey: apiKey
        });
    }
    /**
     * Update the API key for the OpenAI client
     * @param apiKey The new API key
     */
    updateApiKey(apiKey) {
        this.openai = new openai_1.default({
            apiKey: apiKey
        });
    }
    /**
     * Test the connection to the OpenAI API
     * @param apiKey Optional API key to test
     * @returns True if the connection is successful, false otherwise
     */
    async testConnection(apiKey) {
        try {
            // Create a temporary client with the provided API key or use the existing one
            const testClient = apiKey
                ? new openai_1.default({ apiKey })
                : this.openai;
            // Try a simple models list request as a connection test
            const models = await testClient.models.list();
            return models.data.length > 0;
        }
        catch (error) {
            console.error('OpenAI connection test failed:', error);
            return false;
        }
    }
    /**
     * Extract questions from the transcript
     * @param transcript The transcript text
     * @returns Array of extracted questions
     */
    async extractQuestions(transcript) {
        if (!transcript || transcript.trim() === '') {
            return [];
        }
        // Implement incremental processing - only process if transcript has grown significantly
        // This prevents unnecessary API calls for minor transcript changes
        if (this.lastQuestions.length > 0) {
            const lengthDifference = transcript.length - this.lastProcessedLength;
            // Only process if at least 100 new characters have been added
            if (lengthDifference < 100) {
                console.log("Transcript change too small, using last questions");
                return this.lastQuestions;
            }
        }
        // Update the last processed length
        this.lastProcessedLength = transcript.length;
        // Generate a simple hash of the transcript for caching
        const transcriptHash = this.simpleHash(transcript);
        // Check if we have a recent cache entry
        const cacheEntry = this.transcriptCache[transcriptHash];
        if (cacheEntry && (Date.now() - cacheEntry.timestamp < this.CACHE_TTL)) {
            console.log("Using cached questions");
            this.lastQuestions = cacheEntry.questions;
            return cacheEntry.questions;
        }
        // Manage cache size to prevent memory issues
        this.cleanupCache();
        console.log('Extracting questions using OpenAI');
        const startTime = Date.now();
        try {
            const response = await this.openai.chat.completions.create({
                model: 'gpt-4o-mini',
                messages: [
                    {
                        role: 'system',
                        content: 'You are an assistant that extracts interview questions from a transcript. ' +
                            'Follow these guidelines carefully:\n' +
                            '1. Identify only the clear questions being asked to the interviewee\n' +
                            '2. When sequential questions appear in the transcript, combine them into a single question\n' +
                            '3. IMPORTANT: Preserve ALL language specifications mentioned in the question (e.g., "in Python", "using JavaScript")\n' +
                            '4. Include ALL technical requirements and constraints mentioned in the question\n' +
                            '5. Return the questions as a JSON array of strings, with no additional text'
                    },
                    {
                        role: 'user',
                        content: `Extract the interview questions from this transcript: "${transcript}"`
                    }
                ],
                response_format: { type: 'json_object' },
                temperature: 0.1, // Lower temperature for more consistent results
                max_tokens: 1024 // Limit token usage for faster response
            });
            const content = response.choices[0]?.message.content;
            if (!content) {
                return [];
            }
            const endTime = Date.now();
            console.log(`Question extraction completed in ${endTime - startTime}ms`);
            try {
                const parsedContent = JSON.parse(content);
                let questions = Array.isArray(parsedContent.questions) ? parsedContent.questions : [];
                // Post-process questions to ensure quality
                questions = questions
                    .filter((q) => q && q.trim() !== '') // Remove empty questions
                    .map((q) => q.trim()); // Trim whitespace
                // Cache the result with the current timestamp
                this.transcriptCache[transcriptHash] = {
                    questions: questions,
                    timestamp: Date.now()
                };
                // Update the last questions
                this.lastQuestions = questions;
                console.log(`Extracted ${questions.length} questions from transcript`);
                return questions;
            }
            catch (parseError) {
                console.error('Error parsing OpenAI response:', parseError);
                return [];
            }
        }
        catch (error) {
            console.error('Error extracting questions:', error);
            return [];
        }
    }
    /**
     * Generate a simple hash for caching purposes
     * @param str The string to hash
     * @returns A simple numeric hash
     */
    simpleHash(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32bit integer
        }
        return hash.toString();
    }
    /**
     * Clean up old cache entries to prevent memory issues
     */
    cleanupCache() {
        // Clean up transcript cache
        const transcriptKeys = Object.keys(this.transcriptCache);
        if (transcriptKeys.length > this.MAX_CACHE_SIZE) {
            // Sort by timestamp (oldest first)
            const sortedKeys = transcriptKeys.sort((a, b) => this.transcriptCache[a].timestamp - this.transcriptCache[b].timestamp);
            // Remove oldest entries
            const keysToRemove = sortedKeys.slice(0, transcriptKeys.length - this.MAX_CACHE_SIZE);
            keysToRemove.forEach(key => {
                delete this.transcriptCache[key];
            });
            console.log(`Cleaned up ${keysToRemove.length} old transcript cache entries`);
        }
        // Clean up answer cache
        const answerKeys = Object.keys(this.answerCache);
        if (answerKeys.length > this.MAX_CACHE_SIZE) {
            // Sort by timestamp (oldest first)
            const sortedKeys = answerKeys.sort((a, b) => this.answerCache[a].timestamp - this.answerCache[b].timestamp);
            // Remove oldest entries
            const keysToRemove = sortedKeys.slice(0, answerKeys.length - this.MAX_CACHE_SIZE);
            keysToRemove.forEach(key => {
                delete this.answerCache[key];
            });
            console.log(`Cleaned up ${keysToRemove.length} old answer cache entries`);
        }
    }
    /**
     * Clear all caches to free up memory
     */
    clearCache() {
        this.transcriptCache = {};
        this.answerCache = {};
        this.lastQuestions = [];
        this.lastProcessedLength = 0;
        console.log('All caches cleared');
    }
    /**
     * Generate an answer for a given question
     * @param question The question to answer
     * @returns The generated answer
     */
    async generateAnswer(question) {
        if (!question || question.trim() === '') {
            return 'No question provided.';
        }
        // Check cache first
        const questionHash = this.simpleHash(question);
        const cacheEntry = this.answerCache[questionHash];
        if (cacheEntry && (Date.now() - cacheEntry.timestamp < this.CACHE_TTL)) {
            console.log("Using cached answer");
            return cacheEntry.answer;
        }
        // Manage cache size
        this.cleanupCache();
        // Get user settings for context
        const settings = this.appState.getUserSettings();
        // Build the context from available settings
        let context = '';
        if (settings.resume && settings.resume.trim() !== '') {
            context += `\nMy resume: ${settings.resume}`;
        }
        if (settings.jobDescription && settings.jobDescription.trim() !== '') {
            context += `\nJob description: ${settings.jobDescription}`;
        }
        if (settings.additionalContext && settings.additionalContext.trim() !== '') {
            context += `\nAdditional context: ${settings.additionalContext}`;
        }
        try {
            console.log('Generating answer for question');
            const startTime = Date.now();
            const response = await this.openai.chat.completions.create({
                model: 'gpt-4o-mini',
                temperature: 0.7, // Higher temperature for more creative answers
                max_tokens: 1024, // Limit token usage
                messages: [
                    {
                        role: 'system',
                        content: 'You are the job candidate in an interview. ' +
                            'Respond directly to interview questions in first-person voice, as if you are speaking in the interview. ' +
                            'Keep responses concise (100-150 words maximum), conversational, authentic, and confident. ' +
                            'Focus on your most relevant experience and skills. ' +
                            'Use natural language with occasional filler words ("um", "you know") for authenticity. ' +
                            'Be brief but impactful - get to the point quickly. ' +
                            'Avoid sounding rehearsed or robotic. ' +
                            'Speak naturally as in a real interview but keep it direct. ' +
                            'You can use markdown formatting for emphasis or to highlight code examples. ' +
                            'When mentioning code, use proper markdown code blocks with language specification, e.g., ```javascript. ' +
                            'Use **bold** for emphasis on key points.' +
                            (context ? `\n\nHere is information about you and the job you're interviewing for:${context}` : '')
                    },
                    {
                        role: 'user',
                        content: `The interviewer asks: "${question}". Respond as if you are the candidate.`
                    }
                ]
            });
            const endTime = Date.now();
            console.log(`Answer generation completed in ${endTime - startTime}ms`);
            const answer = response.choices[0]?.message.content || 'Unable to generate an answer.';
            // Cache the answer
            this.answerCache[questionHash] = {
                answer: answer,
                timestamp: Date.now()
            };
            return answer;
        }
        catch (error) {
            console.error('Error generating answer:', error);
            return 'Error generating answer. Please try again.';
        }
    }
}
exports.OpenAIHelper = OpenAIHelper;
//# sourceMappingURL=OpenAIHelper.js.map